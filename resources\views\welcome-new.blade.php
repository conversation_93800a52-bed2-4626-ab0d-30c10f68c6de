@extends('layouts.web')

@section('title', 'QualifyRS - Master Your Next Interview')
@section('description', 'Prepare for success with our comprehensive collection of interview questions, quizzes, and expert tips to help you land your dream job.')

@section('content')

<!-- Hero Section -->
<section id="home" class="bg-gradient-to-br from-blue-50 to-indigo-100 py-16 lg:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:grid lg:grid-cols-2 lg:gap-12 items-center">
            <div class="mb-12 lg:mb-0">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Ace Your Next
                    <span class="text-blue-600">Interview</span>
                </h1>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Master interview skills with our comprehensive collection of questions, interactive quizzes, and expert preparation resources designed for students and job seekers.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ route('interview.index') }}" class="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors text-center">
                        Explore Questions
                    </a>
                    <a href="#categories" class="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors text-center">
                        Browse Categories
                    </a>
                </div>
            </div>
            <div class="relative">
                <div class="bg-white rounded-2xl shadow-2xl p-8 transform rotate-2 hover:rotate-0 transition-transform duration-300">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        </div>
                        <div class="text-sm font-medium text-gray-700 mb-2">Interview Question</div>
                        <div class="h-3 bg-gray-200 rounded w-full"></div>
                        <div class="h-3 bg-gray-200 rounded w-3/4"></div>
                        <div class="h-3 bg-blue-200 rounded w-5/6"></div>
                        <div class="mt-6 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg">
                            <div class="text-xs text-blue-600 font-medium mb-2">Your Answer</div>
                            <div class="h-2 bg-blue-200 rounded w-full mb-1"></div>
                            <div class="h-2 bg-blue-200 rounded w-2/3"></div>
                        </div>
                        <div class="flex justify-between items-center mt-4">
                            <span class="text-xs text-green-600 font-medium">✓ Correct</span>
                            <span class="text-xs text-gray-500">Next Question →</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section id="categories" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Popular Categories</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Explore interview questions organized by technology, role, and difficulty level to focus your preparation.
            </p>
        </div>

        @php
            $categories = \App\Models\Category::active()
                ->whereNull('parent_category')
                ->with(['children' => function($query) {
                    $query->active()->orderBy('sort_order');
                }])
                ->orderBy('sort_order')
                ->take(6)
                ->get();
        @endphp

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($categories as $category)
            <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-100 group">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                    <a href="{{ route('interview.category', $category->slug) }}" class="hover:text-blue-600 transition-colors">
                        {{ $category->category }}
                    </a>
                </h3>
                <p class="text-gray-600 leading-relaxed mb-4">
                    {{ $category->description ?: 'Master interview questions in ' . $category->category . ' to boost your confidence and skills.' }}
                </p>
                @if($category->children->count() > 0)
                <div class="flex flex-wrap gap-2">
                    @foreach($category->children->take(3) as $subcategory)
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">{{ $subcategory->category }}</span>
                    @endforeach
                    @if($category->children->count() > 3)
                    <span class="text-xs text-blue-600">+{{ $category->children->count() - 3 }} more</span>
                    @endif
                </div>
                @endif
            </div>
            @endforeach
        </div>

        <div class="text-center mt-10">
            <a href="{{ route('interview.index') }}" class="inline-flex items-center px-6 py-3 border border-blue-600 text-blue-600 font-medium rounded-lg hover:bg-blue-600 hover:text-white transition-colors">
                View All Categories
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose QualifyRS?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to succeed in your next interview, all in one place.
            </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Comprehensive Questions</h3>
                <p class="text-gray-600">
                    Access thousands of interview questions across multiple technologies and difficulty levels.
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Interactive Quizzes</h3>
                <p class="text-gray-600">
                    Test your knowledge with timed quizzes and get instant feedback on your performance.
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Progress Tracking</h3>
                <p class="text-gray-600">
                    Monitor your learning progress and identify areas that need more practice.
                </p>
            </div>

            <!-- Feature 4 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Expert Content</h3>
                <p class="text-gray-600">
                    Learn from industry experts with detailed explanations and best practices.
                </p>
            </div>

            <!-- Feature 5 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Flexible Learning</h3>
                <p class="text-gray-600">
                    Study at your own pace, anytime, anywhere with our mobile-friendly platform.
                </p>
            </div>

            <!-- Feature 6 -->
            <div class="text-center">
                <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Community Support</h3>
                <p class="text-gray-600">
                    Join thousands of students and professionals preparing for their dream jobs.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-16 bg-blue-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Join Thousands of Successful Students</h2>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Our platform has helped students and professionals land their dream jobs at top companies.
            </p>
        </div>

        @php
            $stats = [
                'questions' => \App\Models\Question::count(),
                'categories' => \App\Models\Category::count(),
                'users' => \App\Models\User::count(),
                'quizzes' => \App\Models\Quiz::count() ?: 50, // fallback if no quizzes yet
            ];
        @endphp

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">
                    {{ number_format($stats['questions']) }}+
                </div>
                <p class="text-blue-100 font-medium">Interview Questions</p>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">
                    {{ number_format($stats['categories']) }}+
                </div>
                <p class="text-blue-100 font-medium">Categories</p>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">
                    {{ number_format($stats['users']) }}+
                </div>
                <p class="text-blue-100 font-medium">Active Students</p>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-white mb-2">
                    {{ number_format($stats['quizzes']) }}+
                </div>
                <p class="text-blue-100 font-medium">Practice Quizzes</p>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section id="cta" class="py-16 bg-gradient-to-br from-indigo-600 to-blue-700">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Ready to Start Your Interview Preparation?</h2>
        <p class="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
            Join thousands of students who have successfully prepared for their interviews using our comprehensive platform.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            @guest
            <a href="{{ route('register') }}" class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors">
                Get Started Free
            </a>
            <a href="{{ route('interview.index') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors">
                Browse Questions
            </a>
            @else
            <a href="{{ route('dashboard') }}" class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors">
                Go to Dashboard
            </a>
            <a href="{{ route('interview.index') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors">
                Continue Learning
            </a>
            @endguest
        </div>
    </div>
</section>
@endsection
