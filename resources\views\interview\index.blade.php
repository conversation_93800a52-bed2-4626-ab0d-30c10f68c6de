@extends('layouts.web')

@section('title', 'Interview Questions - Master Your Next Interview | QualifyRS')
@section('description', 'Prepare for success with our comprehensive collection of interview questions across various technologies and roles. Practice with real questions from top companies.')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
            </svg>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                    Master Your Next
                    <span class="text-yellow-400">Interview</span>
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed">
                    Prepare for success with our comprehensive collection of interview questions from top companies. Practice, learn, and land your dream job.
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white">{{ number_format(\App\Models\Question::count()) }}+</div>
                        <div class="text-blue-200 text-sm">Questions</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white">{{ number_format(\App\Models\Category::count()) }}+</div>
                        <div class="text-blue-200 text-sm">Categories</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white">{{ number_format(\App\Models\User::count()) }}+</div>
                        <div class="text-blue-200 text-sm">Students</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white">95%</div>
                        <div class="text-blue-200 text-sm">Success Rate</div>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#categories" class="inline-flex items-center px-8 py-4 bg-yellow-400 text-gray-900 font-semibold rounded-xl hover:bg-yellow-300 transition-colors shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Start Practicing Now
                    </a>
                    <a href="#search" class="inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-blue-600 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search Questions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <form action="{{ route('interview.search') }}" method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="q" value="{{ request('search') }}" 
                           placeholder="Search interview questions..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Search
                </button>
            </form>
        </div>

        <!-- Categories Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            @foreach($categories as $category)
                <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">
                            <a href="{{ route('interview.category', $category->slug) }}" class="hover:text-blue-600">
                                {{ $category->category }}
                            </a>
                        </h3>
                        <p class="text-gray-600 mb-4">{{ $category->description }}</p>
                        
                        @if($category->children->count() > 0)
                            <div class="space-y-2">
                                @foreach($category->children->take(3) as $child)
                                    <a href="{{ route('interview.category', $child->slug) }}" 
                                       class="block text-sm text-blue-600 hover:text-blue-800">
                                        {{ $child->category }}
                                    </a>
                                @endforeach
                                @if($category->children->count() > 3)
                                    <p class="text-sm text-gray-500">+{{ $category->children->count() - 3 }} more</p>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Difficulty Levels -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Browse by Difficulty</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="{{ route('interview.difficulty', 'beginner') }}" 
                   class="p-4 text-center bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <div class="text-green-600 font-semibold">Beginner</div>
                    <div class="text-sm text-green-500">Entry Level</div>
                </a>
                <a href="{{ route('interview.difficulty', 'intermediate') }}" 
                   class="p-4 text-center bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors">
                    <div class="text-yellow-600 font-semibold">Intermediate</div>
                    <div class="text-sm text-yellow-500">Mid Level</div>
                </a>
                <a href="{{ route('interview.difficulty', 'advanced') }}" 
                   class="p-4 text-center bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                    <div class="text-orange-600 font-semibold">Advanced</div>
                    <div class="text-sm text-orange-500">Senior Level</div>
                </a>
                <a href="{{ route('interview.difficulty', 'expert') }}" 
                   class="p-4 text-center bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                    <div class="text-red-600 font-semibold">Expert</div>
                    <div class="text-sm text-red-500">Lead Level</div>
                </a>
            </div>
        </div>

        <!-- Recent Questions -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Recent Questions</h2>
            <div class="space-y-6">
                @forelse($questions as $question)
                    <div class="border-b border-gray-200 pb-6 last:border-b-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="{{ route('interview.show', $question->id) }}" class="hover:text-blue-600">
                                        {{ $question->title }}
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-3">{{ Str::limit($question->question, 200) }}</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $question->category->category }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($question->difficulty === 'beginner') bg-green-100 text-green-800
                                        @elseif($question->difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                                        @elseif($question->difficulty === 'advanced') bg-orange-100 text-orange-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($question->difficulty) }}
                                    </span>
                                    <span>{{ $question->views }} views</span>
                                    <span>{{ $question->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12">
                        <div class="text-gray-500 text-lg">No questions found.</div>
                        <p class="text-gray-400 mt-2">Check back later for new interview questions.</p>
                    </div>
                @endforelse
            </div>

            @if($questions->hasPages())
                <div class="mt-8">
                    {{ $questions->links() }}
                </div>
            @endif
        </div>

        <!-- Popular Tags -->
        @if($popularTags->count() > 0)
            <div class="bg-white rounded-lg shadow-sm p-6 mt-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Popular Tags</h2>
                <div class="flex flex-wrap gap-2">
                    @foreach($popularTags as $tag)
                        <a href="{{ route('interview.tag', $tag->slug) }}" 
                           class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white hover:opacity-80 transition-opacity"
                           style="background-color: {{ $tag->color }}">
                            {{ $tag->name }}
                            <span class="ml-1 text-xs opacity-75">({{ $tag->usage_count }})</span>
                        </a>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
