<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#faviconGradient)"/>
  
  <!-- Icon: Person reading book -->
  <g transform="translate(8, 6)">
    <!-- Book -->
    <rect x="2" y="14" width="12" height="8" rx="1" fill="white" opacity="0.9"/>
    <line x1="8" y1="14" x2="8" y2="22" stroke="url(#faviconGradient)" stroke-width="0.8"/>
    <line x1="4" y1="16" x2="6" y2="16" stroke="url(#faviconGradient)" stroke-width="0.6"/>
    <line x1="10" y1="16" x2="12" y2="16" stroke="url(#faviconGradient)" stroke-width="0.6"/>
    <line x1="4" y1="18" x2="6" y2="18" stroke="url(#faviconGradient)" stroke-width="0.6"/>
    <line x1="10" y1="18" x2="12" y2="18" stroke="url(#faviconGradient)" stroke-width="0.6"/>
    
    <!-- Person head -->
    <circle cx="8" cy="6" r="3" fill="white"/>
    
    <!-- Person body -->
    <path d="M 5 9 Q 8 8 11 9 L 11 14 L 5 14 Z" fill="white" opacity="0.9"/>
  </g>
</svg>
