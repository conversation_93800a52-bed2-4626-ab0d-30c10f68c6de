<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ __('Edit Page') }}
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Editing: {{ $page->title }}
                </p>
            </div>
            <div class="flex space-x-2">
                <a href="{{ $page->category ? route('web.pages.show', [$page->category->slug, $page->slug]) : route('web.pages.show.simple', $page->slug) }}"
                   class="btn-success" target="_blank">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View Page
                </a>
                <a href="{{ route('pages.manage') }}" class="btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Manage
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="px-4 sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg" role="alert">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>{{ session('success') }}</span>
                    </div>
                </div>
            @endif
            <form action="{{ route('pages.update', $page) }}" method="POST" x-data="pageForm()">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
                    <!-- Main Content -->
                    <div class="xl:col-span-3 space-y-6">
                        <!-- Basic Information Card -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Basic Information</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Title -->
                                    <div class="md:col-span-2">
                                        <label for="title" class="form-label">Page Title *</label>
                                        <input type="text"
                                               id="title"
                                               name="title"
                                               value="{{ old('title', $page->title) }}"
                                               class="form-input"
                                               required
                                               autofocus
                                               x-model="title"
                                               @input="generateSlug"
                                               placeholder="Enter an engaging title for your page">
                                        @error('title')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Slug -->
                                    <div class="md:col-span-2">
                                        <label for="slug" class="form-label">URL Slug</label>
                                        <div class="relative">
                                            <input type="text"
                                                   id="slug"
                                                   name="slug"
                                                   value="{{ old('slug', $page->slug) }}"
                                                   class="form-input pl-20"
                                                   x-model="slug"
                                                   placeholder="auto-generated-from-title">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 text-sm">/pages/</span>
                                            </div>
                                        </div>
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">URL-friendly version of the title.</p>
                                        @error('slug')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Content Card -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">Page Content</h3>
                                <div class="form-group">
                                    <x-quill-editor
                                        name="content"
                                        :value="old('content', $page->content)"
                                        placeholder="Start writing your page content here..."
                                        height="450px"
                                        required />
                                </div>
                            </div>
                        </div>

                        <!-- SEO & Meta Card -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-header">SEO & Meta Information</h3>
                                <div class="form-group">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea id="meta_description"
                                              name="meta_description"
                                              rows="3"
                                              class="form-textarea"
                                              maxlength="160"
                                              placeholder="Write a compelling description for search engines...">{{ old('meta_description', $page->meta_description) }}</textarea>
                                    <div class="flex justify-between mt-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Brief description for search engines and social media.</p>
                                        <span class="text-xs text-gray-400" x-text="$el.previousElementSibling.value.length + '/160'"></span>
                                    </div>
                                    @error('meta_description')
                                        <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                    <input type="text"
                                           id="meta_keywords"
                                           name="meta_keywords"
                                           value="{{ old('meta_keywords', $page->meta_keywords) }}"
                                           class="form-input"
                                           placeholder="keyword1, keyword2, keyword3">
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Comma-separated keywords related to this page.</p>
                                    @error('meta_keywords')
                                        <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                            <!-- Sidebar -->
                            <div class="space-y-6">
                                <!-- Page Info -->
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Page Information</h3>
                                    <div class="space-y-2 text-sm text-gray-600">
                                        <div><strong>Created:</strong> {{ $page->created_at->format('M j, Y g:i A') }}</div>
                                        <div><strong>Updated:</strong> {{ $page->updated_at->format('M j, Y g:i A') }}</div>
                                        <div><strong>Views:</strong> {{ $page->views }}</div>
                                        <div><strong>Author:</strong> {{ $page->author->name }}</div>
                                    </div>
                                </div>

                                <!-- Publish Settings -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Publish Settings</h3>
                                    
                                    <div class="space-y-4">
                                        <!-- Status -->
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="status" value="1" 
                                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                                       {{ old('status', $page->status) ? 'checked' : '' }}>
                                                <span class="ml-2 text-sm text-gray-600">Published</span>
                                            </label>
                                        </div>

                                        <!-- Featured -->
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="featured" value="1" 
                                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                                       {{ old('featured', $page->featured) ? 'checked' : '' }}>
                                                <span class="ml-2 text-sm text-gray-600">Mark as featured</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Category</h3>
                                    <select name="category_id" 
                                            class="w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" 
                                                    {{ old('category_id', $page->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->category }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('category_id')" />
                                </div>

                                <!-- Tags -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                                    <div x-data="tagSelector()" class="space-y-2">
                                        <div class="relative">
                                            <input type="text" x-model="search" @input="searchTags" @keydown.enter.prevent="addTag"
                                                   placeholder="Search and select tags..."
                                                   class="w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                            
                                            <div x-show="showDropdown && filteredTags.length > 0" 
                                                 class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
                                                <template x-for="tag in filteredTags" :key="tag.id">
                                                    <div @click="selectTag(tag)" 
                                                         class="px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center justify-between">
                                                        <span x-text="tag.name"></span>
                                                        <span class="w-3 h-3 rounded-full" :style="`background-color: ${tag.color}`"></span>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div class="flex flex-wrap gap-2">
                                            <template x-for="tag in selectedTags" :key="tag.id">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                                      :style="`background-color: ${tag.color}20; color: ${tag.color};`">
                                                    <span x-text="tag.name"></span>
                                                    <button type="button" @click="removeTag(tag.id)" class="ml-1 text-xs">×</button>
                                                    <input type="hidden" name="tags[]" :value="tag.id">
                                                </span>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex flex-col space-y-2">
                                    <x-primary-button type="submit">
                                        {{ __('Update Page') }}
                                    </x-primary-button>
                                    <x-secondary-button type="button" onclick="window.history.back()">
                                        {{ __('Cancel') }}
                                    </x-secondary-button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function pageForm() {
            return {
                title: '{{ old('title', $page->title) }}',
                slug: '{{ old('slug', $page->slug) }}',
                generateSlug() {
                    // Only auto-generate if slug is empty
                    if (this.title && !this.slug) {
                        this.slug = this.title.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim('-');
                    }
                }
            }
        }

        function tagSelector() {
            return {
                search: '',
                showDropdown: false,
                selectedTags: @json($page->tags),
                filteredTags: [],
                availableTags: @json($tags),
                
                searchTags() {
                    if (this.search.length > 0) {
                        this.filteredTags = this.availableTags.filter(tag => 
                            tag.name.toLowerCase().includes(this.search.toLowerCase()) &&
                            !this.selectedTags.find(selected => selected.id === tag.id)
                        );
                        this.showDropdown = true;
                    } else {
                        this.showDropdown = false;
                    }
                },
                
                selectTag(tag) {
                    this.selectedTags.push(tag);
                    this.search = '';
                    this.showDropdown = false;
                },
                
                removeTag(tagId) {
                    this.selectedTags = this.selectedTags.filter(tag => tag.id !== tagId);
                },
                
                addTag() {
                    if (this.filteredTags.length > 0) {
                        this.selectTag(this.filteredTags[0]);
                    }
                }
            }
        }
    </script>
</x-app-layout>
